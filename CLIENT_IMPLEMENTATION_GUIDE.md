# Piano WebSocket Client Implementation Guide

Piano WebSocketサーバーに接続するクライアントの実装方法を説明します。

## 概要

Piano WebSocketサーバーは以下の機能を提供します：
- **自動ユーザー管理**: 接続時にランダムなユーザーID・名前を自動生成
- **MIDIイベント配信**: リアルタイムでMIDIイベントを他のクライアントに配信
- **ユーザー一覧管理**: アクティブユーザーの管理と通知
- **メモリリーク対策**: 自動クリーンアップとリソース管理

## 接続フロー

### 1. WebSocket接続
```
Client -> Server: WebSocket接続要求
Server -> Client: connected メッセージ（ユーザー情報付き）
```

### 2. 自動ユーザー作成
サーバーが自動的に以下を生成：
- **ユーザーID**: `user_` + タイムスタンプ + ランダム文字列
- **ユーザー名**: `ユーザー123` のような自動生成名

### 3. 即座に利用可能
認証手続き不要で即座にMIDI送信・受信が可能

## メッセージフォーマット

### JSON基本構造
```json
{
  "type": "メッセージタイプ",
  "timestamp": 1640995200000,
  // その他のフィールド
}
```

## クライアント→サーバー メッセージ

### 1. MIDIイベント送信
```json
{
  "type": "midi",
  "buffers": [
    [144, 60, 80, 255, 0, 0, 0, 0],
    [128, 60, 0, 255, 0, 0, 100, 0]
  ]
}
```

**バッファ構造（8バイト）:**
- `buffer[0]`: ステータスバイト（144=NoteOn, 128=NoteOff）
- `buffer[1]`: ノート番号（0-127）
- `buffer[2]`: ベロシティ（0-127）
- `buffer[3]`: 色情報R（0-255）
- `buffer[4]`: 色情報G（0-255）
- `buffer[5]`: 色情報B（0-255）
- `buffer[6]`: デルタタイム上位バイト（0-255）
- `buffer[7]`: デルタタイム下位バイト（0-255）

**デルタタイム:**
- 前のイベントからの相対時間（ミリ秒）
- 16ビット値（0-65535ms）として格納
- 最初のイベントのデルタタイムは0

**複数イベント送信:**
- `buffers` は配列の配列として複数のMIDIイベントを一度に送信可能
- 各イベントのデルタタイムは前のイベントからの相対時間
- 単一イベントの場合も `buffers: [buffer]` の形式で送信

### 2. ユーザー名変更
```json
{
  "type": "update_username",
  "username": "新しいユーザー名"
}
```

### 3. チャットメッセージ送信
```json
{
  "type": "chat",
  "message": "こんにちは、みなさん！"
}
```

### 4. ユーザー一覧取得
```json
{
  "type": "get_users"
}
```

### 5. ユーザー名設定
```json
{
  "type": "set_username",
  "username": "新しいユーザー名"
}
```

### 6. Ping送信
```json
{
  "type": "ping"
}
```

## サーバー→クライアント メッセージ

### 1. 接続確認
```json
{
  "type": "connected",
  "clientId": "1640995200123abc",
  "user": {
    "id": "user_abc123",
    "username": "ユーザー456"
  },
  "activeUsers": [
    {
      "id": "user_abc123",
      "username": "ユーザー456",
      "connectedClients": 1
    }
  ],
  "timestamp": 1640995200000
}
```

### 2. MIDIイベント受信
```json
{
  "type": "midi",
  "buffers": [
    [144, 60, 80, 255, 0, 0, 0, 0],
    [128, 60, 0, 255, 0, 0, 100, 0]
  ],
  "fromUser": {
    "id": "user_def456",
    "username": "送信者名"
  },
  "timestamp": 1640995200000
}
```

### 3. ユーザー名更新成功
```json
{
  "type": "username_updated",
  "username": "新しいユーザー名",
  "timestamp": 1640995200000
}
```

### 4. ユーザー一覧更新
```json
{
  "type": "user_update",
  "users": [
    {
      "id": "user_abc123",
      "username": "ユーザー456",
      "connectedClients": 2
    }
  ],
  "timestamp": 1640995200000
}
```

### 5. エラーメッセージ
```json
{
  "type": "error",
  "message": "エラーの詳細",
  "timestamp": 1640995200000
}
```

### 6. Pong応答
```json
{
  "type": "pong",
  "timestamp": 1640995200000
}
```

### 7. MIDI受信確認
```json
{
  "type": "midi_ack",
  "timestamp": 1640995200000
}
```

### 8. チャットメッセージ
```json
{
  "type": "chat",
  "message": "こんにちは！",
  "fromUser": {
    "id": "user_abc123",
    "username": "ユーザー123"
  },
  "timestamp": 1640995200000
}
```

## 実装例

### JavaScript (ブラウザ)
```javascript
class PianoClient {
  constructor() {
    this.ws = null;
    this.userId = null;
    this.username = null;
  }
  
  connect() {
    this.ws = new WebSocket('ws://localhost:9191');
    
    this.ws.onopen = () => {
      console.log('Connected to server');
    };
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleMessage(data);
    };
  }
  
  handleMessage(data) {
    switch (data.type) {
      case 'connected':
        this.userId = data.user.id;
        this.username = data.user.username;
        console.log(`Connected as: ${this.username}`);
        break;
        
      case 'midi':
        console.log(`MIDI from ${data.fromUser.username}`);
        this.handleMidiEvent(data.buffer);
        break;

      case 'chat':
        this.handleChatMessage(data);
        break;

      case 'user_update':
        this.updateUserList(data.users);
        break;

      case 'pong':
        console.log('Pong received');
        break;

      case 'error':
        console.error('Server error:', data.message);
        break;
    }
  }
  
  sendNoteOn(notenum, velocity, color) {
    const deltaTime = 0; // 最初のイベントまたは前のイベントからの時間差（ms）
    const buffer = [
      144, // Note On
      notenum,
      velocity,
      (color >> 16) & 0xFF, // R
      (color >> 8) & 0xFF,  // G
      color & 0xFF,         // B
      (deltaTime >> 8) & 0xFF, // デルタタイム上位
      deltaTime & 0xFF         // デルタタイム下位
    ];

    this.ws.send(JSON.stringify({
      type: 'midi',
      buffers: [buffer]
    }));
  }

  sendChatMessage(message) {
    if (message.trim()) {
      this.ws.send(JSON.stringify({
        type: 'chat',
        message: message.trim()
      }));
    }
  }

  handleChatMessage(data) {
    const timestamp = new Date(data.timestamp).toLocaleTimeString();
    const username = data.fromUser ? data.fromUser.username : 'Unknown';
    const message = data.message || '';

    console.log(`[${timestamp}] ${username}: ${message}`);
    // ここでチャットUIを更新
  }
}
```

### Python
```python
import websocket
import json
import time

class PianoClient:
    def __init__(self):
        self.ws = None
        self.user_id = None
        self.username = None
    
    def connect(self, url="ws://localhost:9191"):
        self.ws = websocket.WebSocketApp(url,
            on_open=self.on_open,
            on_message=self.on_message,
            on_error=self.on_error,
            on_close=self.on_close)
        
        self.ws.run_forever()
    
    def on_message(self, ws, message):
        data = json.loads(message)
        
        if data['type'] == 'connected':
            self.user_id = data['user']['id']
            self.username = data['user']['username']
            print(f"Connected as: {self.username}")
            
        elif data['type'] == 'midi':
            print(f"MIDI from {data['fromUser']['username']}")

        elif data['type'] == 'chat':
            self.handle_chat_message(data)

    def handle_chat_message(self, data):
        timestamp = time.strftime('%H:%M:%S', time.localtime(data['timestamp'] / 1000))
        username = data['fromUser']['username'] if 'fromUser' in data else 'Unknown'
        message = data.get('message', '')
        print(f"[{timestamp}] {username}: {message}")

    def send_chat_message(self, message):
        if message.strip():
            self.ws.send(json.dumps({
                'type': 'chat',
                'message': message.strip()
            }))

    def send_note_on(self, notenum, velocity, color):
        delta_time = 0  # 最初のイベントまたは前のイベントからの時間差（ms）
        buffer = [
            144,  # Note On
            notenum,
            velocity,
            (color >> 16) & 0xFF,  # R
            (color >> 8) & 0xFF,   # G
            color & 0xFF,          # B
            (delta_time >> 8) & 0xFF,  # デルタタイム上位
            delta_time & 0xFF          # デルタタイム下位
        ]

        message = {
            'type': 'midi',
            'buffers': [buffer]
        }
        
        self.ws.send(json.dumps(message))
```

## 重要な実装ポイント

### 1. 接続状態管理
- WebSocket接続状態を適切に管理
- 再接続機能の実装を推奨
- エラーハンドリングの実装

### 2. MIDIバッファ処理
- 8バイト固定長のバッファ形式
- 色情報はRGB各8ビット
- デルタタイム情報で同期制御
- 複数バッファを配列として送信可能

### 3. ユーザー情報管理
- 接続時に自動生成されるユーザー情報を保存
- ユーザー一覧の更新を適切に処理
- ユーザー名変更機能の実装

### 4. エラーハンドリング
- ネットワークエラーの処理
- JSON解析エラーの処理
- WebSocket切断時の処理

### 5. パフォーマンス考慮
- 大量のMIDIイベント送信時の制御
- メモリリークの防止
- 適切なリソース管理

## テスト方法

### 1. 基本接続テスト
```javascript
// 接続テスト
const client = new PianoClient();
client.connect();
```

### 2. MIDI送信テスト
```javascript
// C4ノートを赤色で送信
client.sendNoteOn(60, 80, 0xFF0000);

// 1秒後に停止
setTimeout(() => {
  client.sendNoteOff(60, 0, 0xFF0000);
}, 1000);
```

### 3. マルチクライアントテスト
- 複数のクライアントで同時接続
- 相互のMIDIイベント受信確認
- ユーザー一覧の同期確認

## トラブルシューティング

### よくある問題

1. **接続できない**
   - サーバーが起動しているか確認
   - ポート番号（9191）が正しいか確認
   - ファイアウォール設定を確認

2. **MIDIイベントが受信されない**
   - JSON形式が正しいか確認
   - バッファサイズが8バイトか確認
   - WebSocket接続状態を確認

3. **ユーザー情報が更新されない**
   - メッセージハンドラーが正しく実装されているか確認
   - user_update メッセージを適切に処理しているか確認

### デバッグ方法

1. **ログ出力**
   - 送受信メッセージをログ出力
   - 接続状態の変化をログ出力

2. **ブラウザ開発者ツール**
   - Network タブでWebSocket通信を確認
   - Console でエラーメッセージを確認

3. **サーバーログ**
   - サーバー側のログでクライアント接続を確認
   - エラーメッセージの確認

## 言語別実装例

### C++
詳細な実装は `cpp-client-example/` ディレクトリを参照してください。

**特徴:**
- libwebsockets使用
- マルチスレッド対応
- コールバック方式
- RAII による自動リソース管理

### Node.js
```javascript
const WebSocket = require('ws');

class PianoClient {
  constructor() {
    this.ws = null;
  }

  connect() {
    this.ws = new WebSocket('ws://localhost:9191');

    this.ws.on('open', () => {
      console.log('Connected to server');
    });

    this.ws.on('message', (data) => {
      const message = JSON.parse(data);
      this.handleMessage(message);
    });
  }

  sendMidi(buffer) {
    this.ws.send(JSON.stringify({
      type: 'midi',
      buffer: buffer
    }));
  }
}
```

### Go
```go
package main

import (
    "encoding/json"
    "log"
    "github.com/gorilla/websocket"
)

type PianoClient struct {
    conn *websocket.Conn
    userID string
    username string
}

func (c *PianoClient) Connect(url string) error {
    conn, _, err := websocket.DefaultDialer.Dial(url, nil)
    if err != nil {
        return err
    }
    c.conn = conn

    go c.readMessages()
    return nil
}

func (c *PianoClient) SendMidi(buffer []int) error {
    message := map[string]interface{}{
        "type": "midi",
        "buffer": buffer,
    }

    return c.conn.WriteJSON(message)
}
```

### Rust
```rust
use tokio_tungstenite::{connect_async, tungstenite::Message};
use serde_json::json;

pub struct PianoClient {
    // WebSocket connection
}

impl PianoClient {
    pub async fn connect(&mut self, url: &str) -> Result<(), Box<dyn std::error::Error>> {
        let (ws_stream, _) = connect_async(url).await?;
        // Handle connection
        Ok(())
    }

    pub async fn send_midi(&mut self, buffer: Vec<u8>) -> Result<(), Box<dyn std::error::Error>> {
        let message = json!({
            "type": "midi",
            "buffer": buffer
        });

        // Send message
        Ok(())
    }
}
```

## セキュリティ考慮事項

### 1. 入力検証
- MIDIバッファサイズの検証（8バイト固定）
- ノート番号の範囲チェック（0-127）
- ベロシティの範囲チェック（0-127）
- ユーザー名の長さ制限

### 2. レート制限
- MIDI送信頻度の制限
- 大量送信の防止
- DoS攻撃対策

### 3. データ検証
- JSON形式の検証
- 必須フィールドの確認
- 型チェック

## パフォーマンス最適化

### 1. 接続管理
- 接続プールの使用
- 再接続戦略の実装
- ハートビート機能

### 2. メッセージ処理
- バッファリング
- バッチ処理
- 非同期処理

### 3. メモリ管理
- オブジェクトプールの使用
- ガベージコレクション対策
- リソースリーク防止

## 拡張機能

### 1. 録音・再生機能
- MIDIシーケンスの録音
- 録音データの再生
- ファイル保存・読み込み

### 2. 楽器選択
- 音色情報の追加
- 楽器変更メッセージ
- サウンドフォント対応

### 3. ルーム機能
- 複数ルームの作成
- ルーム間の移動
- プライベートセッション

### 4. チャット機能拡張
- チャット履歴の保存
- プライベートメッセージ
- メッセージの削除・編集
- 絵文字・スタンプ対応

## チャット機能の実装詳細

### 基本的な使い方

1. **チャットメッセージ送信**
   ```javascript
   client.sendChatMessage("こんにちは！");
   ```

2. **チャットメッセージ受信処理**
   ```javascript
   handleChatMessage(data) {
     const timestamp = new Date(data.timestamp).toLocaleTimeString();
     const username = data.fromUser.username;
     const message = data.message;
     console.log(`[${timestamp}] ${username}: ${message}`);
   }
   ```

3. **メッセージ制限**
   - 最大1000文字
   - 空メッセージは送信されない
   - HTMLエスケープ処理推奨

これらの機能追加時は、サーバー側の対応も必要になります。
