# チャット機能ガイド

## 概要

Piano WebSocketサーバーにリアルタイムチャット機能が追加されました。接続中の全ユーザーがリアルタイムでメッセージを交換できます。

## 機能仕様

### 基本機能
- **リアルタイムメッセージング**: 全接続クライアントに即座に配信
- **ユーザー識別**: 送信者のユーザー名とタイムスタンプ表示
- **メッセージ制限**: 1000文字制限でスパム防止
- **入力検証**: 空メッセージや不正データの除外

### セキュリティ機能
- メッセージ長制限（1000文字）
- HTMLエスケープ処理推奨
- 不正データの自動除外

## メッセージフォーマット

### クライアント→サーバー（チャット送信）
```json
{
  "type": "chat",
  "message": "こんにちは、みなさん！",
  "timestamp": 1640995200000
}
```

### サーバー→クライアント（チャット受信）
```json
{
  "type": "chat",
  "message": "こんにちは、みなさん！",
  "fromUser": {
    "id": "user_abc123",
    "username": "ユーザー123"
  },
  "timestamp": 1640995200000
}
```

## 実装例

### JavaScript (ブラウザ)

#### チャット送信
```javascript
function sendChatMessage(message) {
  if (this.ws && this.ws.readyState === WebSocket.OPEN && message.trim()) {
    this.ws.send(JSON.stringify({
      type: 'chat',
      message: message.trim(),
      timestamp: Date.now()
    }));
  }
}
```

#### チャット受信処理
```javascript
function handleChatMessage(data) {
  const timestamp = new Date(data.timestamp).toLocaleTimeString();
  const username = data.fromUser ? data.fromUser.username : 'Unknown';
  const message = data.message || '';
  
  // チャットログに表示
  console.log(`[${timestamp}] ${username}: ${message}`);
  
  // UIに表示（例）
  const chatLog = document.getElementById('chatLog');
  const messageElement = document.createElement('div');
  messageElement.className = 'chat-message';
  messageElement.innerHTML = `
    <span class="timestamp">[${timestamp}]</span>
    <span class="username">${username}:</span>
    <span class="message">${message}</span>
  `;
  chatLog.appendChild(messageElement);
  chatLog.scrollTop = chatLog.scrollHeight;
}
```

#### メッセージハンドラーに追加
```javascript
handleMessage(data) {
  switch (data.type) {
    case 'connected':
      // 接続処理
      break;
      
    case 'midi':
      // MIDI処理
      break;
      
    case 'chat':
      this.handleChatMessage(data);
      break;
      
    // その他のケース...
  }
}
```

### Python

#### チャット送信
```python
def send_chat_message(self, message):
    if message.strip():
        self.ws.send(json.dumps({
            'type': 'chat',
            'message': message.strip(),
            'timestamp': int(time.time() * 1000)
        }))
```

#### チャット受信処理
```python
def handle_chat_message(self, data):
    timestamp = time.strftime('%H:%M:%S', time.localtime(data['timestamp'] / 1000))
    username = data['fromUser']['username'] if 'fromUser' in data else 'Unknown'
    message = data.get('message', '')
    print(f"[{timestamp}] {username}: {message}")
```

### C++

#### チャット送信
```cpp
void PianoClient::sendChatMessage(const std::string& message) {
    if (message.empty() || message.length() > 1000) return;
    
    json chatMsg;
    chatMsg["type"] = "chat";
    chatMsg["message"] = message;
    chatMsg["timestamp"] = getCurrentTimestamp();
    
    std::string msgStr = chatMsg.dump();
    lws_write(wsi, (unsigned char*)msgStr.c_str(), msgStr.length(), LWS_WRITE_TEXT);
}
```

#### チャット受信処理
```cpp
void PianoClient::handleChatMessage(const json& data) {
    auto timestamp = std::chrono::system_clock::from_time_t(data["timestamp"].get<long long>() / 1000);
    auto time_t = std::chrono::system_clock::to_time_t(timestamp);
    
    std::string username = data["fromUser"]["username"].get<std::string>();
    std::string message = data["message"].get<std::string>();
    
    std::cout << "[" << std::put_time(std::localtime(&time_t), "%H:%M:%S") 
              << "] " << username << ": " << message << std::endl;
}
```

## UI実装例

### HTML構造
```html
<div class="chat-section">
  <h3>チャット</h3>
  <div id="chatLog" class="chat-log"></div>
  <div class="chat-input">
    <input type="text" id="chatInput" placeholder="メッセージを入力..." maxlength="1000">
    <button onclick="sendChat()">送信</button>
  </div>
</div>
```

### CSS スタイル
```css
.chat-section {
  margin: 20px 0;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background: #f9f9f9;
}

.chat-log {
  height: 200px;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 10px;
  background: white;
  margin-bottom: 10px;
}

.chat-input {
  display: flex;
  gap: 10px;
}

.chat-input input {
  flex: 1;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 3px;
}

.chat-input button {
  padding: 8px 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
}

.chat-message {
  margin-bottom: 5px;
  color: #0066cc;
  font-weight: bold;
}

.timestamp {
  color: #666;
  font-size: 0.9em;
}

.username {
  color: #333;
  font-weight: bold;
}

.message {
  color: #000;
  font-weight: normal;
}
```

### JavaScript イベント処理
```javascript
// Enterキーでの送信
function handleChatKeyPress(event) {
  if (event.key === 'Enter') {
    sendChat();
  }
}

// チャット送信
function sendChat() {
  const input = document.getElementById('chatInput');
  const message = input.value.trim();
  if (message) {
    client.sendChatMessage(message);
    input.value = '';
  }
}
```

## 注意事項

1. **メッセージ制限**: 1000文字を超えるメッセージは自動的に切り詰められます
2. **HTMLエスケープ**: XSS攻撃を防ぐため、メッセージ表示時はHTMLエスケープを行ってください
3. **接続状態確認**: メッセージ送信前にWebSocket接続状態を確認してください
4. **エラーハンドリング**: ネットワークエラーやJSON解析エラーに対応してください
5. **UI更新**: チャットログの自動スクロールやメッセージ数制限を実装することを推奨します

## 今後の拡張予定

- チャット履歴の保存
- プライベートメッセージ機能
- メッセージの削除・編集
- 絵文字・スタンプ対応
- ルーム別チャット
- メッセージ検索機能
