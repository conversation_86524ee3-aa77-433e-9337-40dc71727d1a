<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Piano WebSocket Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .piano {
            display: flex;
            margin: 20px 0;
            height: 200px;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        .key {
            flex: 1;
            border: 1px solid #ccc;
            cursor: pointer;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            padding: 10px;
            font-size: 12px;
            transition: all 0.1s;
            position: relative;
        }
        .key.white {
            background: white;
            color: #333;
        }
        .key.black {
            background: #333;
            color: white;
            height: 120px;
            margin: 0 -10px;
            z-index: 2;
            flex: 0.6;
        }
        .key.active {
            transform: scale(0.95);
        }
        .controls {
            margin: 20px 0;
        }
        .controls button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .connect { background-color: #28a745; color: white; }
        .disconnect { background-color: #dc3545; color: white; }
        .user-setup {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
        }
        .user-setup input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
            width: 200px;
        }
        .user-setup button {
            padding: 8px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            background-color: #007bff;
            color: white;
        }
        .user-setup button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .user-list {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
            max-height: 150px;
            overflow-y: auto;
        }
        .user-item {
            padding: 5px 10px;
            margin: 2px 0;
            background: white;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
        .log {
            height: 200px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 10px;
            background: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .velocity-control {
            margin: 10px 0;
        }
        .velocity-control input {
            width: 200px;
        }

        .chat-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }

        .chat-input {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .chat-input input {
            flex: 1;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 3px;
        }

        .chat-input button {
            padding: 8px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .chat-input button:hover {
            background: #0056b3;
        }

        .chat-message {
            color: #0066cc;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Piano WebSocket Client</h1>
        
        <div id="status" class="status disconnected">未接続</div>

        <div class="user-setup">
            <h3>ユーザー設定</h3>
            <input type="text" id="username" placeholder="ユーザー名を変更（オプション）" maxlength="20">
            <button id="updateUsernameBtn" disabled>ユーザー名更新</button>
            <small>接続時にランダムなユーザー名が自動生成されます</small>
        </div>

        <div class="controls">
            <button id="connectBtn" class="connect">接続</button>
            <button id="disconnectBtn" class="disconnect" disabled>切断</button>
        </div>

        <div class="user-list" id="userListContainer" style="display: none;">
            <h3>接続中のユーザー</h3>
            <div id="userList"></div>
        </div>

        <div class="chat-section">
            <h3>チャット</h3>
            <div class="chat-input">
                <input type="text" id="chatInput" placeholder="メッセージを入力..." maxlength="1000" onkeypress="handleChatKeyPress(event)">
                <button onclick="sendChat()">送信</button>
            </div>
        </div>

        <div class="velocity-control">
            <label for="velocity">ベロシティ: <span id="velocityValue">64</span></label>
            <input type="range" id="velocity" min="1" max="127" value="64">
        </div>
        
        <div class="piano" id="piano">
            <!-- ピアノキーは動的に生成 -->
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        class PianoClient {
            constructor() {
                this.ws = null;
                this.clientId = null;
                this.userId = null;
                this.username = null;
                this.isAuthenticated = false;
                this.velocity = 64;
                this.activeKeys = new Set();
                this.activeUsers = [];

                this.initializeUI();
                this.createPiano();
            }
            
            initializeUI() {
                document.getElementById('connectBtn').addEventListener('click', () => this.connect());
                document.getElementById('disconnectBtn').addEventListener('click', () => this.disconnect());
                document.getElementById('updateUsernameBtn').addEventListener('click', () => this.updateUsername());

                const velocitySlider = document.getElementById('velocity');
                velocitySlider.addEventListener('input', (e) => {
                    this.velocity = parseInt(e.target.value);
                    document.getElementById('velocityValue').textContent = this.velocity;
                });

                // Enterキーでユーザー名更新
                document.getElementById('username').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !document.getElementById('updateUsernameBtn').disabled) {
                        this.updateUsername();
                    }
                });
            }
            
            createPiano() {
                const piano = document.getElementById('piano');
                const notes = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
                
                // 2オクターブ分のキーを作成（C4-B5）
                for (let octave = 4; octave <= 5; octave++) {
                    for (let i = 0; i < notes.length; i++) {
                        const note = notes[i];
                        const noteNum = 60 + (octave - 4) * 12 + i; // C4 = 60
                        const key = document.createElement('div');
                        
                        key.className = note.includes('#') ? 'key black' : 'key white';
                        key.textContent = `${note}${octave}`;
                        key.dataset.notenum = noteNum;
                        
                        key.addEventListener('mousedown', (e) => this.noteOn(noteNum, e));
                        key.addEventListener('mouseup', (e) => this.noteOff(noteNum, e));
                        key.addEventListener('mouseleave', (e) => this.noteOff(noteNum, e));
                        
                        piano.appendChild(key);
                    }
                }
            }
            
            connect() {
                try {
                    this.ws = new WebSocket('ws://localhost:9191');

                    this.ws.onopen = () => {
                        this.updateStatus('接続中...', 'connected');
                        this.log('WebSocket接続が開かれました');
                    };
                    
                    this.ws.onmessage = (event) => {
                        try {
                            const data = JSON.parse(event.data);
                            this.handleMessage(data);
                        } catch (error) {
                            this.log(`Raw message: ${event.data}`);
                        }
                    };
                    
                    this.ws.onclose = () => {
                        this.updateStatus('切断されました', 'disconnected');
                        this.log('WebSocket接続が閉じられました');
                        this.resetUI();
                    };
                    
                    this.ws.onerror = (error) => {
                        this.log(`WebSocketエラー: ${error}`);
                    };
                    
                } catch (error) {
                    this.log(`接続エラー: ${error.message}`);
                }
            }
            
            disconnect() {
                if (this.ws) {
                    this.ws.close();
                }
            }
            
            handleMessage(data) {
                switch (data.type) {
                    case 'connected':
                        this.clientId = data.clientId;
                        this.userId = data.user.id;
                        this.username = data.user.username;
                        this.activeUsers = data.activeUsers;

                        this.updateStatus(`接続済み - ${this.username}`, 'connected');
                        this.log(`接続完了: ${this.username} (${this.userId})`);
                        this.updateUI();
                        this.updateUserList();
                        break;

                    case 'username_updated':
                        this.username = data.username;
                        this.updateStatus(`接続済み - ${this.username}`, 'connected');
                        this.log(`ユーザー名を更新: ${this.username}`);
                        document.getElementById('username').value = '';
                        break;

                    case 'midi':
                        if (data.fromUser) {
                            this.log(`MIDI受信 from ${data.fromUser.username}:`);
                        }
                        this.handleMidiBuffers(data.buffers || [data.buffer]); // 新旧両対応
                        break;

                    case 'user_update':
                    case 'user_list':
                        this.activeUsers = data.users;
                        this.updateUserList();
                        break;

                    case 'chat':
                        this.handleChatMessage(data);
                        break;

                    case 'pong':
                        this.log('Pong受信');
                        break;

                    case 'error':
                        this.log(`エラー: ${data.message}`);
                        break;

                    default:
                        this.log(`未知のメッセージタイプ: ${data.type}`);
                }
            }

            updateUsername() {
                const newUsername = document.getElementById('username').value.trim();
                if (!newUsername) {
                    alert('ユーザー名を入力してください');
                    return;
                }

                this.sendMessage({
                    type: 'update_username',
                    username: newUsername
                });
            }

            updateUI() {
                document.getElementById('connectBtn').disabled = true;
                document.getElementById('disconnectBtn').disabled = false;
                document.getElementById('updateUsernameBtn').disabled = false;
                document.getElementById('userListContainer').style.display = 'block';
            }

            resetUI() {
                document.getElementById('connectBtn').disabled = false;
                document.getElementById('disconnectBtn').disabled = true;
                document.getElementById('updateUsernameBtn').disabled = true;
                document.getElementById('userListContainer').style.display = 'none';
                document.getElementById('username').value = '';
                this.activeUsers = [];
                this.updateUserList();
            }

            updateUserList() {
                const userList = document.getElementById('userList');
                userList.innerHTML = '';

                this.activeUsers.forEach(user => {
                    const userItem = document.createElement('div');
                    userItem.className = 'user-item';
                    userItem.innerHTML = `
                        <strong>${user.username}</strong>
                        <small>(${user.connectedClients} 接続)</small>
                    `;
                    userList.appendChild(userItem);
                });

                if (this.activeUsers.length === 0) {
                    userList.innerHTML = '<div style="color: #666;">接続中のユーザーはいません</div>';
                }
            }

            handleMidiBuffers(buffers) {
                // 複数バッファからMIDIイベントを解析
                buffers.forEach(buffer => {
                    const events = this.parseMidiBuffer(buffer);
                    events.forEach(event => {
                        this.log(`MIDI受信: ${event.type} note=${event.notenum} vel=${event.velocity} color=${event.color} deltaTime=${event.deltaTime}ms`);
                        this.scheduleNoteVisualization(event);
                    });
                });
            }

            parseMidiBuffer(buffer) {
                const events = [];

                // バッファが配列として送られてくることを想定
                if (Array.isArray(buffer)) {
                    for (let i = 0; i < buffer.length; i += 8) { // 8バイトずつ処理
                        if (i + 7 < buffer.length) {
                            const deltaTime = (buffer[i + 6] << 8) | buffer[i + 7];
                            const event = {
                                type: buffer[i] === 144 ? 'noteon' : buffer[i] === 128 ? 'noteoff' : 'unknown',
                                notenum: buffer[i + 1],
                                velocity: buffer[i + 2],
                                color: `#${buffer[i + 3].toString(16).padStart(2, '0')}${buffer[i + 4].toString(16).padStart(2, '0')}${buffer[i + 5].toString(16).padStart(2, '0')}`,
                                deltaTime: deltaTime
                            };
                            events.push(event);
                        }
                    }
                }

                return events;
            }
            
            scheduleNoteVisualization(event) {
                // デルタタイムに基づいてイベントをスケジュール
                setTimeout(() => {
                    this.visualizeNote(event);
                }, event.deltaTime || 0);
            }

            visualizeNote(event) {
                const key = document.querySelector(`[data-notenum="${event.notenum}"]`);
                if (key) {
                    if (event.type === 'noteon') {
                        key.style.backgroundColor = event.color;
                        key.classList.add('active');
                    } else if (event.type === 'noteoff') {
                        key.style.backgroundColor = '';
                        key.classList.remove('active');
                    }
                }
            }
            
            noteOn(noteNum, event) {
                if (this.activeKeys.has(noteNum)) return;
                
                this.activeKeys.add(noteNum);
                const key = event.target;
                key.classList.add('active');
                
                // ランダムな色を生成
                const color = this.generateRandomColor();
                key.style.backgroundColor = color;
                
                this.sendMidiEvent('noteon', noteNum, this.velocity, color);
                this.log(`Note ON: ${noteNum} vel=${this.velocity} color=${color}`);
            }
            
            noteOff(noteNum, event) {
                if (!this.activeKeys.has(noteNum)) return;
                
                this.activeKeys.delete(noteNum);
                const key = event.target;
                key.classList.remove('active');
                key.style.backgroundColor = '';
                
                this.sendMidiEvent('noteoff', noteNum, 0, '#000000');
                this.log(`Note OFF: ${noteNum}`);
            }
            
            generateRandomColor() {
                const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
                return colors[Math.floor(Math.random() * colors.length)];
            }
            
            sendMidiEvent(type, notenum, velocity, color, deltaTime = 0) {
                if (!this.ws || this.ws.readyState !== WebSocket.OPEN || !this.userId) return;

                // MIDIバッファを作成（8バイト形式、デルタタイム付き）
                const buffer = [
                    type === 'noteon' ? 144 : 128, // ステータスバイト
                    notenum,                       // ノート番号
                    velocity,                      // ベロシティ
                    parseInt(color.substr(1, 2), 16), // R
                    parseInt(color.substr(3, 2), 16), // G
                    parseInt(color.substr(5, 2), 16), // B
                    (deltaTime >> 8) & 0xFF,       // デルタタイム上位
                    deltaTime & 0xFF               // デルタタイム下位
                ];

                this.sendMessage({
                    type: 'midi',
                    buffers: [buffer] // 配列の配列として送信
                });
            }

            sendMessage(message) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify(message));
                }
            }

            sendChatMessage(message) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN && message.trim()) {
                    this.ws.send(JSON.stringify({
                        type: 'chat',
                        message: message.trim(),
                        timestamp: Date.now()
                    }));
                }
            }

            handleChatMessage(data) {
                const timestamp = new Date(data.timestamp).toLocaleTimeString();
                const username = data.fromUser ? data.fromUser.username : 'Unknown';
                const message = data.message || '';

                this.log(`[${timestamp}] ${username}: ${message}`, 'chat');
            }
            
            updateStatus(message, className) {
                const status = document.getElementById('status');
                status.textContent = message;
                status.className = `status ${className}`;
            }
            
            log(message, type = 'info') {
                const log = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                const className = type === 'chat' ? 'chat-message' : '';
                log.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
                log.scrollTop = log.scrollHeight;
            }
        }
        
        // チャット関連の関数
        function sendChat() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();
            if (message) {
                client.sendChatMessage(message);
                input.value = '';
            }
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendChat();
            }
        }

        // アプリケーション開始
        const client = new PianoClient();
    </script>
</body>
</html>
